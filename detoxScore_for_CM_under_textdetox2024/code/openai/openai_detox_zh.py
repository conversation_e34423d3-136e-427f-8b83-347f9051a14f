from openai import OpenAI
import re
import json
import pandas as pd
import os
import time

# 获取当前脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 读取数据文件 - 数据文件在上级目录的data文件夹中
data_path = os.path.join(script_dir, "..", "data", "CM", "ZH.tsv")
df_c = pd.read_csv(data_path, sep="\t")

# 读取配置文件
config_path = os.path.join(script_dir, "../config.json")
with open(config_path, "r") as f:
    config = json.load(f)

deepseek_config = config.get("openai")


def extract_json_from_string(input_string):
    """从字符串中提取JSON内容"""
    # 使用正则表达式匹配 JSON 部分
    json_match = re.search(r"\{.*\}", input_string, re.DOTALL)
    if json_match:
        json_str = json_match.group(0)
        # 将 JSON 字符串中的双引号替换为单引号
        json_str = json_str.replace('""', '"')
        # 将 JSON 字符串转换为字典
        json_dict = json.loads(json_str)
        return json_dict
    else:
        raise ValueError("未找到有效的 JSON 内容")


# 初始化OpenAI客户端，使用DeepSeek API
client = OpenAI(
    api_key=deepseek_config["apiKey"],
    base_url=deepseek_config["baseUrl"]
)


def normalize_score(score):
    """
    将评分标准化为0、0.5、1三个值
    """
    if score >= 0.75:
        return 1.0
    elif score >= 0.25:
        return 0.5
    else:
        return 0.0


def calculate_joint_score(sta, cs, fs):
    """
    计算联合分数(Joint Score)
    根据论文，JS是STA、CS、FS的综合评分
    这里使用简单的平均值，实际可能需要加权平均
    """
    return (sta + cs + fs) / 3


def etd_few_shot_evaluation(toxic_sentence, neutral_sentence, retry_count=5, initial_delay=30):
    """
    使用ETD框架进行少样本文本解毒评估
    包含示例来帮助模型理解评估标准
    """
    delay = initial_delay

    for attempt in range(retry_count):
        try:
            response = client.chat.completions.create(
                model=deepseek_config["model"],
                temperature=deepseek_config["temperature"],
                messages=[
                    {
                        "role": "user",
                        "content": f"""你现在是文本解毒任务的评估专家。请根据ETD评估框架对以下文本解毒结果进行评估。

<背景介绍>
文本解毒任务旨在将包含有毒内容的句子改写为中性句子，同时保持原有含义。你需要从风格迁移准确性、内容保留度和流畅性三个维度进行评估。
</背景介绍>

<任务描述>
请评估给定的有毒文本和中性文本对，从以下三个维度给出评分：
1. 风格迁移准确性(STA): 评估中性文本是否成功去除了有毒内容
2. 内容保留度(CS): 评估中性文本是否保持了原文的核心意思
3. 流畅性(FS): 评估中性文本的语言质量和可读性
</任务描述>

<评价指标>
1. 风格迁移准确性(STA): 只有0、0.5、1三个分数
   - 1.0: 完全去除有毒内容，语言完全中性
   - 0.5: 部分有毒内容被去除，仍有一些有毒表达
   - 0.0: 几乎没有去除有毒内容

2. 内容保留度(CS): 只有0、0.5、1三个分数
   - 1.0: 完全保持原意，核心信息无损失
   - 0.5: 部分保持原意，有一定信息损失
   - 0.0: 几乎没有保持原意

3. 流畅性(FS): 只有0、0.5、1三个分数
   - 1.0: 语言完全流畅，无语法错误
   - 0.5: 可以理解，但有一些语法或表达问题
   - 0.0: 难以理解，语法错误严重
</评价指标>

<任务示例>
示例1:
有毒文本: "any of us who did half this shit would be locked the fuck up already."
中性文本: "any of us who did half of these actions would likely be incarcerated already."
评估结果: {{"STA": 1.0, "CS": 1.0, "FS": 1.0, "j_score": 1.0, "reasoning": "成功将粗俗语言转换为正式表达，完全保持了原意，语言流畅自然"}}

示例2:
有毒文本: "are they liars or just clueless?"
中性文本: "are they dishonest or simply unaware?"
评估结果: {{"STA": 1.0, "CS": 1.0, "FS": 1.0, "j_score": 1.0, "reasoning": "将带有攻击性的词汇替换为中性词汇，意思保持一致，表达更加礼貌"}}

示例3:
有毒文本: "cretins like this rarely care about facts."
中性文本: "people with such behavior rarely care about facts."
评估结果: {{"STA": 1.0, "CS": 0.5, "FS": 1.0, "j_score": 0.83, "reasoning": "去除了侮辱性词汇，用中性描述替代，基本保持原意但略有泛化"}}
</任务示例>

<输入数据格式>
有毒文本: {toxic_sentence}
中性文本: {neutral_sentence}
</输入数据格式>

<输出数据格式>
请严格按照以下JSON格式输出评估结果：

```json
{{
"STA": number, // 风格迁移准确性分数 (只能是0、0.5或1)
"CS": number,  // 内容保留度分数 (只能是0、0.5或1)
"FS": number,  // 流畅性分数 (只能是0、0.5或1)
"j_score": number,  // 联合分数 (自动计算的平均值)
"reasoning": "string" // 详细的评估理由，解释各项分数的依据
}}
```
</输出数据格式>

请仔细分析给定的文本对，参考示例中的评估标准，给出客观、准确的评估结果。""",
                    }
                ],
            )

            result = extract_json_from_string(response.choices[0].message.content)

            # 标准化评分为0、0.5、1三个值
            if 'STA' in result:
                result['STA'] = normalize_score(result['STA'])
            if 'CS' in result:
                result['CS'] = normalize_score(result['CS'])
            if 'FS' in result:
                result['FS'] = normalize_score(result['FS'])

            # 计算联合分数
            if 'STA' in result and 'CS' in result and 'FS' in result:
                result['j_score'] = calculate_joint_score(result['STA'], result['CS'], result['FS'])

            return result

        except Exception as e:
            error_str = str(e)
            print(f"第 {attempt + 1} 次尝试失败: {e}")

            # 检查不同类型的错误并采用不同的重试策略
            if any(keyword in error_str.lower() for keyword in ["429", "rate limit", "负载已饱和", "too many requests"]):
                if attempt < retry_count - 1:
                    print(f"检测到限流错误，等待 {delay} 秒后重试...")
                    time.sleep(delay)
                    delay = min(delay * 1.5, 300)  # 最大等待5分钟
                else:
                    print("达到最大重试次数，放弃处理")
                    return None
            elif any(keyword in error_str.lower() for keyword in ["timeout", "connection", "network"]):
                if attempt < retry_count - 1:
                    print(f"检测到网络错误，等待 {min(delay//2, 15)} 秒后重试...")
                    time.sleep(min(delay//2, 15))
                else:
                    print("网络错误重试失败")
                    return None
            else:
                # 其他错误，较短等待时间
                if attempt < retry_count - 1:
                    print(f"其他错误，等待 10 秒后重试...")
                    time.sleep(10)
                else:
                    print("所有重试都失败了")
                    return None


def etd_zero_shot_evaluation(toxic_sentence, neutral_sentence, retry_count=5, initial_delay=30):
    """
    使用ETD框架进行零样本文本解毒评估
    根据论文要求评估STA、CS、FS三个核心指标
    """
    delay = initial_delay
    
    for attempt in range(retry_count):
        try:
            response = client.chat.completions.create(
                model=deepseek_config["model"],
                temperature=deepseek_config["temperature"],
                messages=[
                    {
                        "role": "user",
                        "content": f"""你现在是文本解毒任务的评估专家。请根据ETD评估框架对以下文本解毒结果进行评估。

<背景介绍>
文本解毒任务旨在将包含有毒内容的句子改写为中性句子，同时保持原有含义。你需要从风格迁移准确性、内容保留度和流畅性三个维度进行评估。
</背景介绍>

<任务描述>
请评估给定的有毒文本和中性文本对，从以下三个维度给出评分：
1. 风格迁移准确性(STA): 评估中性文本是否成功去除了有毒内容
2. 内容保留度(CS): 评估中性文本是否保持了原文的核心意思
3. 流畅性(FS): 评估中性文本的语言质量和可读性
</任务描述>

<评价指标>
1. 风格迁移准确性(STA): 只有0、0.5、1三个分数
   - 1.0: 完全去除有毒内容，语言完全中性
   - 0.5: 部分有毒内容被去除，仍有一些有毒表达
   - 0.0: 几乎没有去除有毒内容

2. 内容保留度(CS): 只有0、0.5、1三个分数
   - 1.0: 完全保持原意，核心信息无损失
   - 0.5: 部分保持原意，有一定信息损失
   - 0.0: 几乎没有保持原意

3. 流畅性(FS): 只有0、0.5、1三个分数
   - 1.0: 语言完全流畅，无语法错误
   - 0.5: 可以理解，但有一些语法或表达问题
   - 0.0: 难以理解，语法错误严重
</评价指标>

<输入数据格式>
有毒文本: {toxic_sentence}
中性文本: {neutral_sentence}
</输入数据格式>

<输出数据格式>
请严格按照以下JSON格式输出评估结果：

```json
{{
"STA": number, // 风格迁移准确性分数 (只能是0、0.5或1)
"CS": number,  // 内容保留度分数 (只能是0、0.5或1)
"FS": number,  // 流畅性分数 (只能是0、0.5或1)
"j_score": number,  // 联合分数 (自动计算的平均值)
"reasoning": "string" // 详细的评估理由，解释各项分数的依据
}}
```
</输出数据格式>

请仔细分析给定的文本对，给出客观、准确的评估结果。""",
                    }
                ],
            )
            
            result = extract_json_from_string(response.choices[0].message.content)

            # 标准化评分为0、0.5、1三个值
            if 'STA' in result:
                result['STA'] = normalize_score(result['STA'])
            if 'CS' in result:
                result['CS'] = normalize_score(result['CS'])
            if 'FS' in result:
                result['FS'] = normalize_score(result['FS'])

            # 计算联合分数
            if 'STA' in result and 'CS' in result and 'FS' in result:
                result['j_score'] = calculate_joint_score(result['STA'], result['CS'], result['FS'])

            return result
            
        except Exception as e:
            error_str = str(e)
            print(f"第 {attempt + 1} 次尝试失败: {e}")
            
            # 检查不同类型的错误并采用不同的重试策略
            if any(keyword in error_str.lower() for keyword in ["429", "rate limit", "负载已饱和", "too many requests"]):
                if attempt < retry_count - 1:
                    print(f"检测到限流错误，等待 {delay} 秒后重试...")
                    time.sleep(delay)
                    delay = min(delay * 1.5, 300)  # 最大等待5分钟
                else:
                    print("达到最大重试次数，放弃处理")
                    return None
            elif any(keyword in error_str.lower() for keyword in ["timeout", "connection", "network"]):
                if attempt < retry_count - 1:
                    print(f"检测到网络错误，等待 {min(delay//2, 15)} 秒后重试...")
                    time.sleep(min(delay//2, 15))
                else:
                    print("网络错误重试失败")
                    return None
            else:
                # 其他错误，较短等待时间
                if attempt < retry_count - 1:
                    print(f"其他错误，等待 10 秒后重试...")
                    time.sleep(10)
                else:
                    print("所有重试都失败了")
                    return None


def find_missing_data(df_original, df_current):
    """找出未处理的数据索引"""
    # 获取已处理的句子对
    processed_pairs = set()
    for _, row in df_current.iterrows():
        processed_pairs.add((row['toxic_sentence'], row['neutral_sentence']))
    
    # 找出未处理的数据索引
    missing_indices = []
    for idx, row in df_original.iterrows():
        pair = (row['toxic_sentence'], row['neutral_sentence'])
        if pair not in processed_pairs:
            missing_indices.append(idx)
    
    return missing_indices


def main():
    """主函数"""
    # 让用户选择评估方法
    print("请选择评估方法:")
    print("1. Zero-shot 评估 (不提供示例)")
    print("2. Few-shot 评估 (提供示例)")

    while True:
        choice = input("请输入选择 (1 或 2): ").strip()
        if choice == "1":
            evaluation_method = "zero_shot"
            evaluation_func = etd_zero_shot_evaluation
            results_filename = os.path.join(script_dir, "etd_zero_shot_results_zh.csv")
            break
        elif choice == "2":
            evaluation_method = "few_shot"
            evaluation_func = etd_few_shot_evaluation
            results_filename = os.path.join(script_dir, "etd_few_shot_results_zh.csv")
            break
        else:
            print("无效选择，请输入 1 或 2")

    print(f"已选择 {evaluation_method} 评估方法")

    # 创建结果文件路径
    
    # 检查是否存在已有结果文件
    if os.path.exists(results_filename):
        df_current = pd.read_csv(results_filename)
        print(f"发现已有结果文件，已处理 {len(df_current)} 条数据")
        
        # 找出未处理的数据
        missing_indices = find_missing_data(df_c, df_current)
        if not missing_indices:
            print("所有数据都已处理完成！")
            return
        
        print(f"还有 {len(missing_indices)} 条数据需要处理")
        start_indices = missing_indices
    else:
        # 创建新的结果文件
        df_current = pd.DataFrame(
            columns=[
                "toxic_sentence",
                "neutral_sentence", 
                "STA",  # 风格迁移准确性
                "CS",   # 内容保留度
                "FS",   # 流畅性
                "j_score",   # 联合分数
                "reasoning",  # 评估理由
            ]
        )
        df_current.to_csv(results_filename, index=False)
        start_indices = list(range(len(df_c)))
        print(f"创建新的结果文件，开始处理 {len(df_c)} 条数据...")
    
    # 处理数据
    success_count = 0
    new_results = []
    
    for i, idx in enumerate(start_indices):
        if idx < len(df_c):
            row = df_c.iloc[idx]
            print(f"\n正在处理第 {idx+1} 条数据 ({i+1}/{len(start_indices)})...")
            print(f"有毒句子: {row['toxic_sentence'][:50]}{'...' if len(row['toxic_sentence']) > 50 else ''}")
            print(f"中性句子: {row['neutral_sentence'][:50]}{'...' if len(row['neutral_sentence']) > 50 else ''}")
            
            result = evaluation_func(row["toxic_sentence"], row["neutral_sentence"])
            
            if result:
                new_row = {
                    "toxic_sentence": row["toxic_sentence"],
                    "neutral_sentence": row["neutral_sentence"],
                    "STA": result["STA"],
                    "CS": result["CS"],
                    "FS": result["FS"],
                    "j_score": result["j_score"],
                    "reasoning": result["reasoning"]
                }
                new_results.append(new_row)
                success_count += 1
                print(f"✅ 第 {idx+1} 条数据处理成功 (成功: {success_count}/{i+1})")
                print(f"结果: STA={result['STA']}, CS={result['CS']}, FS={result['FS']}, j_score={result['j_score']:.2f}")
                
                # 每处理成功3条数据就保存一次，避免数据丢失
                if len(new_results) % 3 == 0:
                    df_new = pd.DataFrame(new_results)
                    df_updated = pd.concat([df_current, df_new], ignore_index=True)
                    df_updated.to_csv(results_filename, index=False)
                    print(f"💾 已保存 {len(new_results)} 条新数据到文件")
                    df_current = df_updated  # 更新当前数据框
                    new_results = []  # 清空临时结果
                    
            else:
                print(f"❌ 第 {idx+1} 条数据处理失败")
                
            # 每处理完一条数据后短暂休息，避免请求过于频繁
            if i < len(start_indices) - 1:  # 不是最后一条数据
                time.sleep(2)
    
    # 保存剩余的结果
    if new_results:
        df_new = pd.DataFrame(new_results)
        df_updated = pd.concat([df_current, df_new], ignore_index=True)
        df_updated.to_csv(results_filename, index=False)
        print(f"\n💾 最终保存完成")
    
    # 重新读取最终结果并计算统计信息
    df_final = pd.read_csv(results_filename)
    
    print(f"\n📊 处理总结:")
    print(f"   - 尝试处理: {len(start_indices)} 条数据")
    print(f"   - 成功处理: {success_count} 条数据")
    print(f"   - 失败数量: {len(start_indices) - success_count} 条数据")
    print(f"   - 成功率: {success_count/len(start_indices)*100:.1f}%")
    print(f"   - 文件中总数据量: {len(df_final)} 条")
    
    # 计算各指标的平均分
    if len(df_final) > 0:
        print(f"\n📈 评估结果统计:")
        print(f"   - STA平均分: {df_final['STA'].mean():.2f}")
        print(f"   - CS平均分: {df_final['CS'].mean():.2f}")
        print(f"   - FS平均分: {df_final['FS'].mean():.2f}")
        print(f"   - JS平均分: {df_final['j_score'].mean():.2f}")
    
    if len(df_final) < len(df_c):
        remaining = len(df_c) - len(df_final)
        print(f"   - 仍有 {remaining} 条数据未处理，可以再次运行此脚本继续处理")
    else:
        print("🎉 所有数据处理完成！")


if __name__ == "__main__":
    main()

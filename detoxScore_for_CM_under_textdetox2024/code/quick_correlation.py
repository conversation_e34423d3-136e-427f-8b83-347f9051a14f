#!/usr/bin/env python3
"""
快速相关性计算脚本
基于您原有的代码逻辑，但更加通用和便捷
"""

import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr


def quick_correlation_analysis():
    """
    快速相关性分析 - 基于您的原始代码逻辑
    """
    print("🔍 开始快速相关性分析...")
    
    # 配置文件路径 - 可以根据需要修改这些路径
    golden_file = "TDE/EN.tsv"
    model_file_output_dir = "detoxScore_for_CM_under_textdetox2024/code/n_prompt_machine/results/"
    column_name = "j_score"
    
    # 检查黄金标准文件是否存在
    if not os.path.exists(golden_file):
        print(f"❌ 黄金标准文件不存在: {golden_file}")
        print("请检查文件路径或将脚本移动到正确的目录")
        return
    
    # 检查模型输出目录是否存在
    if not os.path.exists(model_file_output_dir):
        print(f"❌ 模型输出目录不存在: {model_file_output_dir}")
        print("请检查目录路径")
        return
    
    # 获取所有CSV文件，排除备份文件
    model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv") and "_backup.csv" not in f]
    model_file_outputs = sorted(model_file_outputs)
    
    if not model_file_outputs:
        print(f"❌ 在目录 {model_file_output_dir} 中未找到任何CSV文件")
        return
    
    print(f"📁 找到 {len(model_file_outputs)} 个模型输出文件")
    print(f"📊 黄金标准文件: {golden_file}")
    print(f"📈 比较列名: {column_name}")
    print("=" * 60)
    
    print_data = []
    
    for model_file_output in model_file_outputs:
        print(f"处理文件: {model_file_output}")
        model_file_path = os.path.join(model_file_output_dir, model_file_output)
        
        try:
            # 读取文件
            df_golden = pd.read_csv(golden_file, sep='\t')
            df_model = pd.read_csv(model_file_path)
            
            # 检查列是否存在
            if column_name not in df_golden.columns:
                print(f"  ❌ 黄金标准文件中未找到列 '{column_name}'")
                continue
            
            if column_name not in df_model.columns:
                print(f"  ❌ 模型文件中未找到列 '{column_name}'")
                continue
            
            # 提取需要的列
            col_golden = df_golden[column_name]
            col_model = df_model[column_name]
            
            # 检查数据长度
            if len(col_golden) != len(col_model):
                print(f"  ⚠️  数据长度不匹配: 黄金标准={len(col_golden)}, 模型={len(col_model)}")
                min_len = min(len(col_golden), len(col_model))
                col_golden = col_golden[:min_len]
                col_model = col_model[:min_len]
            
            # 计算各项指标
            pearson = pearsonr(col_golden, col_model)[0]
            spearman = spearmanr(col_golden, col_model)[0]
            
            print_data.append([model_file_output, pearson, spearman])
            print(f"  ✅ Pearson: {pearson:.4f}, Spearman: {spearman:.4f}")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            continue
    
    if not print_data:
        print("❌ 没有成功处理任何文件")
        return
    
    # 按照 Pearson 系数降序排列
    print_data.sort(key=lambda x: x[1], reverse=True)
    
    print("\n" + "=" * 60)
    print("📊 相关性分析结果 (按 Pearson 系数降序排列):")
    print("=" * 60)
    
    # 输出为markdown格式
    print("| Prompt | Pearson | Spearman |")
    print("| --- | --- | --- |")
    for data in print_data:
        print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))
    
    print(f"\n📈 统计摘要:")
    print(f"   - 处理文件数: {len(print_data)}")
    print(f"   - 最高 Pearson: {max(print_data, key=lambda x: x[1])[1]:.4f} ({max(print_data, key=lambda x: x[1])[0]})")
    print(f"   - 最高 Spearman: {max(print_data, key=lambda x: x[2])[1]:.4f} ({max(print_data, key=lambda x: x[2])[0]})")
    print(f"   - 平均 Pearson: {np.mean([x[1] for x in print_data]):.4f}")
    print(f"   - 平均 Spearman: {np.mean([x[2] for x in print_data]):.4f}")


def custom_correlation_analysis(golden_file, model_dir, column_name="j_score"):
    """
    自定义相关性分析
    
    Args:
        golden_file: 黄金标准文件路径
        model_dir: 模型输出目录
        column_name: 比较的列名
    """
    print(f"🔍 开始自定义相关性分析...")
    print(f"黄金标准文件: {golden_file}")
    print(f"模型输出目录: {model_dir}")
    print(f"比较列名: {column_name}")
    
    # 检查文件和目录
    if not os.path.exists(golden_file):
        print(f"❌ 黄金标准文件不存在: {golden_file}")
        return
    
    if not os.path.exists(model_dir):
        print(f"❌ 模型输出目录不存在: {model_dir}")
        return
    
    # 获取所有CSV文件，排除备份文件
    model_files = [f for f in os.listdir(model_dir) if f.endswith(".csv") and "_backup.csv" not in f]
    model_files = sorted(model_files)
    
    if not model_files:
        print(f"❌ 在目录 {model_dir} 中未找到任何CSV文件")
        return
    
    print(f"📁 找到 {len(model_files)} 个模型输出文件")
    print("=" * 60)
    
    print_data = []
    
    for model_file in model_files:
        print(f"处理文件: {model_file}")
        model_file_path = os.path.join(model_dir, model_file)
        
        try:
            # 读取文件
            if golden_file.endswith('.tsv'):
                df_golden = pd.read_csv(golden_file, sep='\t')
            else:
                df_golden = pd.read_csv(golden_file)
            df_model = pd.read_csv(model_file_path)
            
            # 检查列是否存在
            if column_name not in df_golden.columns:
                print(f"  ❌ 黄金标准文件中未找到列 '{column_name}'")
                continue
            
            if column_name not in df_model.columns:
                print(f"  ❌ 模型文件中未找到列 '{column_name}'")
                continue
            
            # 提取需要的列
            col_golden = df_golden[column_name]
            col_model = df_model[column_name]
            
            # 检查数据长度
            if len(col_golden) != len(col_model):
                print(f"  ⚠️  数据长度不匹配: 黄金标准={len(col_golden)}, 模型={len(col_model)}")
                min_len = min(len(col_golden), len(col_model))
                col_golden = col_golden[:min_len]
                col_model = col_model[:min_len]
            
            # 计算各项指标
            pearson = pearsonr(col_golden, col_model)[0]
            spearman = spearmanr(col_golden, col_model)[0]
            
            print_data.append([model_file, pearson, spearman])
            print(f"  ✅ Pearson: {pearson:.4f}, Spearman: {spearman:.4f}")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            continue
    
    if not print_data:
        print("❌ 没有成功处理任何文件")
        return
    
    # 按照 Pearson 系数降序排列
    print_data.sort(key=lambda x: x[1], reverse=True)
    
    print("\n" + "=" * 60)
    print("📊 相关性分析结果 (按 Pearson 系数降序排列):")
    print("=" * 60)
    
    # 输出为markdown格式
    print("| Prompt | Pearson | Spearman |")
    print("| --- | --- | --- |")
    for data in print_data:
        print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # 使用默认配置
        quick_correlation_analysis()
    elif len(sys.argv) == 3:
        # 自定义黄金标准文件和模型目录
        golden_file = sys.argv[1]
        model_dir = sys.argv[2]
        custom_correlation_analysis(golden_file, model_dir)
    elif len(sys.argv) == 4:
        # 自定义黄金标准文件、模型目录和列名
        golden_file = sys.argv[1]
        model_dir = sys.argv[2]
        column_name = sys.argv[3]
        custom_correlation_analysis(golden_file, model_dir, column_name)
    else:
        print("使用方法:")
        print("  python quick_correlation.py                           # 使用默认配置")
        print("  python quick_correlation.py <golden_file> <model_dir> # 自定义文件和目录")
        print("  python quick_correlation.py <golden_file> <model_dir> <column_name> # 完全自定义")
